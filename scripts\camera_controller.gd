extends Node3D

# Simple third-person camera
var mouse_sensitivity: float = 0.002
var distance: float = 6.0
var height: float = 2.0

# Zoom settings
var min_distance: float = 2.0
var max_distance: float = 15.0
var zoom_speed: float = 0.5
var zoom_smoothing: float = 10.0
var target_distance: float = 6.0

# Camera rotation
var yaw: float = 0.0
var pitch: float = 0.0
var min_pitch: float = -60.0
var max_pitch: float = 60.0

# References
@onready var camera: Camera3D = $Camera3D
var target

func _ready():
	# Capture mouse
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)
	# Initialize target distance
	target_distance = distance

func _input(event):
	if event is InputEventMouseMotion and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		# Rotate camera with mouse
		yaw -= event.relative.x * mouse_sensitivity
		pitch += event.relative.y * mouse_sensitivity  # Reversed for inverted controls
		pitch = clamp(pitch, deg_to_rad(min_pitch), deg_to_rad(max_pitch))

	# Handle mouse wheel zoom
	if event is InputEventMouseButton and Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			target_distance -= zoom_speed
			target_distance = clamp(target_distance, min_distance, max_distance)
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			target_distance += zoom_speed
			target_distance = clamp(target_distance, min_distance, max_distance)

	# Toggle mouse capture
	if Input.is_action_just_pressed("ui_cancel"):
		if Input.get_mouse_mode() == Input.MOUSE_MODE_CAPTURED:
			Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)
		else:
			Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)

func _process(delta):
	if target:
		# Smooth zoom interpolation
		distance = lerp(distance, target_distance, zoom_smoothing * delta)

		# Calculate camera position around target
		var target_pos = target.global_position

		# Calculate offset based on yaw and pitch
		var offset = Vector3()
		offset.x = cos(pitch) * sin(yaw) * distance
		offset.y = sin(pitch) * distance + height
		offset.z = cos(pitch) * cos(yaw) * distance

		# Set camera position
		global_position = target_pos + offset

		# Look at target
		look_at(target_pos + Vector3(0, height, 0), Vector3.UP)

func set_target(new_target):
	target = new_target


